import { Modu<PERSON> } from '@nestjs/common';
import { SearchController } from './search.controller';
import { SearchService } from './search.service';
import { OpenaiModule } from '../openai/openai.module';
import { MCPModule } from '../mcp/mcp.module';
import { ContextModule } from '../context/context.module';

@Module({
  imports: [OpenaiModule, MCPModule, ContextModule],
  controllers: [SearchController],
  providers: [SearchService],
})
export class SearchModule {}
