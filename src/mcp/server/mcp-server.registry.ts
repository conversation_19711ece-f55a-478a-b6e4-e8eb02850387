import { Injectable, Logger } from '@nestjs/common';
import { MCPServerConfig, MCPServerInstance } from '../types/mcp.types';

@Injectable()
export class MCPServerRegistry {
  private readonly logger = new Logger(MCPServerRegistry.name);
  private servers = new Map<string, MCPServerInstance>();

  registerServer(config: MCPServerConfig): string {
    const serverId = `${config.name}-${Date.now()}`;
    const serverInstance: MCPServerInstance = {
      id: serverId,
      config,
      status: 'stopped',
      tools: [],
    };

    this.servers.set(serverId, serverInstance);
    this.logger.log(`Registered MCP server: ${config.name} with ID: ${serverId}`);
    return serverId;
  }

  getServer(serverId: string): MCPServerInstance | undefined {
    return this.servers.get(serverId);
  }

  getAllServers(): MCPServerInstance[] {
    return Array.from(this.servers.values());
  }

  updateServerStatus(serverId: string, status: MCPServerInstance['status']): void {
    const server = this.servers.get(serverId);
    if (server) {
      server.status = status;
      this.logger.log(`Updated server ${serverId} status to: ${status}`);
    }
  }

  removeServer(serverId: string): boolean {
    const removed = this.servers.delete(serverId);
    if (removed) {
      this.logger.log(`Removed server: ${serverId}`);
    }
    return removed;
  }

  getRunningServers(): MCPServerInstance[] {
    return this.getAllServers().filter(server => server.status === 'running');
  }
}
