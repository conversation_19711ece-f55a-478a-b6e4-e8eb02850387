import OpenAI from 'openai';

// MCP SDK types - we'll import these at runtime
export interface MCPClient {
  connect(transport: any): Promise<void>;
  close(): Promise<void>;
  listTools(): Promise<any>;
  callTool(request: { name: string; arguments: any }): Promise<any>;
}

export interface MCPTransport {
  close(): Promise<void>;
  onclose?: () => void;
  onerror?: (error: any) => void;
}

export interface MCPServerConfig {
  name: string;
  command: string;
  args?: string[];
  env?: Record<string, string>;
  cwd?: string;
  timeout?: number;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
  handler: (params: any) => Promise<any>;
  serverId: string;
}

export interface MCPServerInstance {
  id: string;
  config: MCPServerConfig;
  transport?: MCPTransport;
  client?: MCPClient;
  status: 'starting' | 'running' | 'stopped' | 'error';
  tools: MCPTool[];
}

// OpenAI-compatible tool type for seamless integration
export type OpenAITool = OpenAI.Chat.Completions.ChatCompletionTool;
export type OpenAIToolCall = OpenAI.Chat.Completions.ChatCompletionMessageToolCall;
