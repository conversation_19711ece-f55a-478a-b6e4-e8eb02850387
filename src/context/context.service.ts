import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { readFileSync } from 'fs';
import { join } from 'path';

@Injectable()
export class ContextService {
  private readonly logger = new Logger(ContextService.name);
  private systemPrompt: string | null = null;
  private databaseMetadata: string | null = null;

  constructor(private configService: ConfigService) {
    this.loadSystemPrompt();
    this.loadDatabaseMetadata();
    this.logger.log('Context Service initialized successfully');
  }

  getSystemPromptWithContext(): string {
    this.logger.log('Generating system prompt with database context');

    let systemPrompt = this.getSystemPrompt();

    const metadata = this.getDatabaseMetadata();
    if (metadata) {
      systemPrompt += '\n\n## Database Metadata\n\n' + metadata;
    }

    return systemPrompt;
  }

  private loadSystemPrompt(): void {
    try {
      const contextConfig = this.configService.get('context');
      const promptPath = contextConfig.systemPromptFile;

      // Resolve path relative to project root
      const fullPath = join(process.cwd(), promptPath);

      this.systemPrompt = readFileSync(fullPath, 'utf-8');
      this.logger.log(`Loaded system prompt from: ${promptPath}`);
    } catch (error) {
      this.logger.warn(`Failed to load system prompt: ${error.message}`);
      this.systemPrompt = this.getDefaultSystemPrompt();
    }
  }

  private loadDatabaseMetadata(): void {
    try {
      const contextConfig = this.configService.get('context');
      const descriptionPath = contextConfig.descriptionFile;

      // Resolve path relative to project root
      const fullPath = join(process.cwd(), descriptionPath);

      this.databaseMetadata = readFileSync(fullPath, 'utf-8');
      this.logger.log(`Loaded database metadata from: ${descriptionPath}`);
    } catch (error) {
      this.logger.warn(`Failed to load database metadata: ${error.message}`);
      this.databaseMetadata = this.getDefaultMetadata();
    }
  }

  private getSystemPrompt(): string {
    if (this.systemPrompt) {
      return this.systemPrompt;
    }

    return this.getDefaultSystemPrompt();
  }

  private getDatabaseMetadata(): string {
    if (this.databaseMetadata) {
      return this.databaseMetadata;
    }

    return this.getDefaultMetadata();
  }

  private getDefaultSystemPrompt(): string {
    return `You are a helpful database assistant. Follow these steps precisely:

1. Read the user's request carefully.
2. To fetch data, call **exactly one** query tool:
   • **Only** \`SELECT …\` statements.
   • **Never** \`INSERT\`, \`UPDATE\`, \`DELETE\`, \`CREATE\`, \`DROP\`, or \`ALTER\`.
   • Add a reasonable \`LIMIT\` (≤ 50) unless the user explicitly asks for all rows.
3. Wait for the tool result.
4. Compose a concise, helpful answer for the user:
   • Explain in plain language what you did.
   • Summarize or tabulate key rows; do not dump large raw JSON.
   • If no rows are returned, say so and suggest what data might be missing.

Always use the OpenAI **tool-calling interface** (no custom tags or wrappers).
If a question can be answered without the database, respond directly.

When citing table or column names, keep their exact PostgreSQL casing.

(You are connected to a production replica; treat the data with care.)`;
  }

  private getDefaultMetadata(): string {
    return `## DATABASE METADATA OVERVIEW

This database contains various tables for data analysis. The actual database schema will be discovered at runtime by the MCP server.
This file should contain additional metadata and context about the database that helps the AI understand relationships and query patterns.

Create a database metadata file at docs/database-description.md to provide specific context information.`;
  }
}
