import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

@Injectable()
export class OpenaiService {
  private readonly logger = new Logger(OpenaiService.name);
  private openai: OpenAI;
  private defaultModel: string;
  private defaultTemperature: number;

  constructor(private configService: ConfigService) {
    const config = this.configService.get('openai');

    this.logger.log(`Initializing OpenAI service with model: ${config.model}, baseURL: ${config.baseURL}, temperature: ${config.temperature}`);

    this.openai = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
    });

    this.defaultModel = config.model;
    this.defaultTemperature = config.temperature;
    this.logger.log('OpenAI service initialized successfully');
  }

  /**
   * Get the OpenAI client instance for direct use
   */
  getClient(): OpenAI {
    return this.openai;
  }

  /**
   * Get default model name
   */
  getDefaultModel(): string {
    return this.defaultModel;
  }

  /**
   * Get default temperature
   */
  getDefaultTemperature(): number {
    return this.defaultTemperature;
  }

  /**
   * Create chat completion with logging
   */
  async createChatCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParams
  ): Promise<OpenAI.Chat.Completions.ChatCompletion> {
    this.logger.log(`Creating chat completion with model: ${params.model}, messages: ${params.messages.length}, tools: ${params.tools?.length || 0}`);

    if (params.tools && params.tools.length > 0) {
      this.logger.log(`Tools available: ${params.tools.map(t => t.function.name).join(', ')}`);
    }

    try {
      const startTime = Date.now();
      const result = await this.openai.chat.completions.create({
        ...params,
        stream: false, // Ensure we get a ChatCompletion, not a stream
      }) as OpenAI.Chat.Completions.ChatCompletion;
      const duration = Date.now() - startTime;

      this.logger.log(`Chat completion successful in ${duration}ms. Usage: ${JSON.stringify(result.usage)}`);

      if (result.choices[0].message.tool_calls) {
        this.logger.log(`Assistant requested ${result.choices[0].message.tool_calls.length} tool calls`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Chat completion failed: ${error.message}`, error.stack);
      throw error;
    }
  }
}
