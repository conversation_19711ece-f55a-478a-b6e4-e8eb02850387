#!/bin/bash

# Development setup script for SW Search Agent

echo "🚀 Setting up SW Search Agent development environment..."

# Check if .env exists, if not copy from .env.example
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the configuration."
else
    echo "✅ .env file already exists."
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the application
echo "🔨 Building the application..."
npm run build

echo "🎉 Development setup complete!"
echo ""
echo "To start the application:"
echo "  Development mode: npm run dev"
echo "  Production mode: npm run start"
echo "  Docker Compose: docker-compose up --build"
echo ""
echo "Make sure to:"
echo "1. Update your .env file with correct database credentials"
echo "2. Ensure your PostgreSQL database is accessible"
echo "3. Configure your OpenAI/LM Studio settings"
