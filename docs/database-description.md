# Database Metadata and Context

This document provides additional metadata and context about the database to help the AI assistant understand relationships and query patterns. The actual database schema is discovered at runtime by the MCP server.

## Core Tables

### Athletes and Teams
- **master_athlete table**: Contains athlete information including names and identifiers
  - Use `master_athlete_id` to retrieve athlete names and details
  - Primary source for athlete data across the system

### Team Management  
- **roster_team table**: Contains event team information and roster data
  - Event teams are located in this table
  - Links athletes to specific teams and events

### Competition Results
- **results table**: Stores competition outcomes and performance data
  - Contains references to athletes via `master_athlete_id`
  - Links to events and competitions
  - Primary source for performance metrics and rankings

## Key Relationships

- Athletes are referenced throughout the system using `master_athlete_id`
- Team rosters connect athletes to specific events through `roster_team`
- Competition results link back to both athletes and events
- Use JOIN operations to connect athlete names with their team assignments and results

## Query Guidelines

- Always use `master_athlete_id` when joining athlete data
- Team information should be queried from `roster_team` table
- For performance data, start with the `results` table
- Use appropriate JOINs to get complete athlete profiles with team and result information

## Common Query Patterns

1. **Get athlete details**: Query `master_athlete` table by `master_athlete_id`
2. **Find team rosters**: Query `roster_team` table for specific events
3. **Retrieve results**: Query `results` table and JO<PERSON> with athlete/team data
4. **Complete athlete profile**: JOIN across all three main tables using `master_athlete_id`
