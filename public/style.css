* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5rem;
}

header p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.search-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: flex-end;
}

#queryInput {
    flex: 1;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

#queryInput:focus {
    outline: none;
    border-color: #3498db;
}

#searchButton {
    padding: 15px 30px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
    min-width: 120px;
    height: fit-content;
}

#searchButton:hover:not(:disabled) {
    background: #2980b9;
}

#searchButton:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

.results-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    min-height: 400px;
}

.conversation-container {
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.message {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 8px;
    max-width: 80%;
}

.message.user {
    background: #e3f2fd;
    margin-left: auto;
    text-align: right;
}

.message.assistant {
    background: #f5f5f5;
    margin-right: auto;
}

.message.error {
    background: #ffebee;
    border-left: 4px solid #f44336;
}

.message-header {
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.message-content {
    white-space: pre-wrap;
    word-wrap: break-word;
}

.message-content code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.message-content pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 10px 0;
    border: 1px solid #e1e8ed;
}

.conversation-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.secondary-btn {
    padding: 10px 20px;
    background: #95a5a6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.secondary-btn:hover {
    background: #7f8c8d;
}

.tool-calls {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e8ed;
}

.tool-call {
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: #666;
}

.empty-state {
    text-align: center;
    color: #7f8c8d;
    padding: 60px 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #95a5a6;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .message {
        max-width: 95%;
    }
    
    header h1 {
        font-size: 2rem;
    }
}
