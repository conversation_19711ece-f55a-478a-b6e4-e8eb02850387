class SearchApp {
    constructor() {
        this.conversationId = null;
        this.initializeElements();
        this.attachEventListeners();
        this.showEmptyState();
    }

    initializeElements() {
        console.log('Initializing elements...');
        this.searchForm = document.getElementById('searchForm');
        this.queryInput = document.getElementById('queryInput');
        this.searchButton = document.getElementById('searchButton');
        this.buttonText = document.getElementById('buttonText');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.conversationContainer = document.getElementById('conversationContainer');
        this.newConversationBtn = document.getElementById('newConversationBtn');
        this.clearConversationBtn = document.getElementById('clearConversationBtn');

        // Debug: Check if all elements are found
        const elements = {
            searchForm: this.searchForm,
            queryInput: this.queryInput,
            searchButton: this.searchButton,
            buttonText: this.buttonText,
            loadingSpinner: this.loadingSpinner,
            conversationContainer: this.conversationContainer,
            newConversationBtn: this.newConversationBtn,
            clearConversationBtn: this.clearConversationBtn
        };

        for (const [name, element] of Object.entries(elements)) {
            if (!element) {
                console.error(`Element not found: ${name}`);
            } else {
                console.log(`Element found: ${name}`);
            }
        }
    }

    attachEventListeners() {
        console.log('Attaching event listeners...');

        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        }

        if (this.newConversationBtn) {
            this.newConversationBtn.addEventListener('click', () => this.startNewConversation());
        }

        if (this.clearConversationBtn) {
            this.clearConversationBtn.addEventListener('click', () => this.clearCurrentConversation());
        }

        // Auto-resize textarea
        if (this.queryInput) {
            this.queryInput.addEventListener('input', () => {
                this.queryInput.style.height = 'auto';
                this.queryInput.style.height = this.queryInput.scrollHeight + 'px';
            });
        }

        console.log('Event listeners attached successfully');
    }

    async handleSearch(event) {
        event.preventDefault();
        
        const query = this.queryInput.value.trim();
        if (!query) return;

        this.setLoading(true);
        this.addMessage('user', query);
        this.queryInput.value = '';
        this.queryInput.style.height = 'auto';

        try {
            const response = await this.sendSearchRequest(query);
            this.conversationId = response.conversationId;
            this.addMessage('assistant', response.response, response.toolCalls);
        } catch (error) {
            console.error('Search error:', error);
            this.addMessage('error', `Error: ${error.message}`);
        } finally {
            this.setLoading(false);
        }
    }

    async sendSearchRequest(query) {
        const requestBody = {
            query: query,
            conversationId: this.conversationId
        };

        const response = await fetch('/api/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    addMessage(type, content, toolCalls = null) {
        // Remove empty state if it exists
        const emptyState = this.conversationContainer.querySelector('.empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';
        headerDiv.textContent = type === 'user' ? 'You' : 
                               type === 'assistant' ? 'Assistant' : 'Error';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;

        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);

        // Add tool calls information if available
        if (toolCalls && toolCalls.length > 0) {
            const toolCallsDiv = document.createElement('div');
            toolCallsDiv.className = 'tool-calls';
            toolCallsDiv.innerHTML = '<strong>Database operations:</strong>';
            
            toolCalls.forEach(toolCall => {
                const toolDiv = document.createElement('div');
                toolDiv.className = 'tool-call';
                toolDiv.textContent = `• ${toolCall.function.name}`;
                toolCallsDiv.appendChild(toolDiv);
            });
            
            messageDiv.appendChild(toolCallsDiv);
        }

        this.conversationContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    setLoading(isLoading) {
        this.searchButton.disabled = isLoading;
        this.queryInput.disabled = isLoading;
        
        if (isLoading) {
            this.buttonText.classList.add('hidden');
            this.loadingSpinner.classList.remove('hidden');
        } else {
            this.buttonText.classList.remove('hidden');
            this.loadingSpinner.classList.add('hidden');
        }
    }

    scrollToBottom() {
        this.conversationContainer.scrollTop = this.conversationContainer.scrollHeight;
    }

    startNewConversation() {
        this.conversationId = null;
        this.conversationContainer.innerHTML = '';
        this.showEmptyState();
    }

    async clearCurrentConversation() {
        if (this.conversationId) {
            try {
                await fetch(`/api/search/conversation/${this.conversationId}`, {
                    method: 'DELETE'
                });
            } catch (error) {
                console.error('Error clearing conversation:', error);
            }
        }
        this.startNewConversation();
    }

    showEmptyState() {
        const emptyStateDiv = document.createElement('div');
        emptyStateDiv.className = 'empty-state';
        emptyStateDiv.innerHTML = `
            <h3>Welcome to SW Search Agent</h3>
            <p>Start by asking a question about your database. For example:</p>
            <ul style="text-align: left; display: inline-block; margin-top: 15px;">
                <li>"Show me all tables in the database"</li>
                <li>"What's the structure of the users table?"</li>
                <li>"Find all products with price greater than 100"</li>
                <li>"How many records are in each table?"</li>
            </ul>
        `;
        this.conversationContainer.appendChild(emptyStateDiv);
    }
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing SearchApp...');
    try {
        new SearchApp();
        console.log('SearchApp initialized successfully');
    } catch (error) {
        console.error('Error initializing SearchApp:', error);
    }
});
