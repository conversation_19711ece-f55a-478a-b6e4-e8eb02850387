NestJS MCP Client/Server Implementation
Project Structure
src/
├── mcp/
│   ├── server/
│   │   ├── mcp-server.module.ts
│   │   ├── mcp-server.service.ts
│   │   ├── mcp-server.registry.ts
│   │   └── servers/
│   │       ├── filesystem-server.ts
│   │       ├── database-server.ts
│   │       └── web-scraper-server.ts
│   ├── client/
│   │   ├── mcp-client.module.ts
│   │   ├── mcp-client.service.ts
│   │   ├── mcp-client.registry.ts
│   │   └── interfaces/
│   │       └── mcp-tool.interface.ts
│   ├── types/
│   │   └── mcp.types.ts
│   └── mcp.controller.ts
├── openai/
│   ├── openai.module.ts
│   └── openai.service.ts
└── app.module.ts

Dependencies
npm install @modelcontextprotocol/sdk openai

Core Types and Interfaces
src/mcp/types/mcp.types.ts
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

export interface MCPServerConfig {
  name: string;
  command: string;
  args?: string[];
  env?: Record<string, string>;
  cwd?: string;
  timeout?: number;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
  handler: (params: any) => Promise<any>;
  serverId: string;
}

export interface MCPServerInstance {
  id: string;
  config: MCPServerConfig;
  transport?: StdioClientTransport;
  client?: Client;
  status: 'starting' | 'running' | 'stopped' | 'error';
  tools: MCPTool[];
}

src/mcp/client/interfaces/mcp-tool.interface.ts
export interface IMCPTool {
  name: string;
  description: string;
  inputSchema: any;
  execute(params: any): Promise<any>;
}

MCP Server Module
src/mcp/server/mcp-server.module.ts
import { Module } from '@nestjs/common';
import { MCPServerService } from './mcp-server.service';
import { MCPServerRegistry } from './mcp-server.registry';

@Module({
  providers: [MCPServerService, MCPServerRegistry],
  exports: [MCPServerService, MCPServerRegistry],
})
export class MCPServerModule {}

src/mcp/server/mcp-server.registry.ts
import { Injectable, Logger } from '@nestjs/common';
import { MCPServerConfig, MCPServerInstance } from '../types/mcp.types';

@Injectable()
export class MCPServerRegistry {
  private readonly logger = new Logger(MCPServerRegistry.name);
  private servers = new Map<string, MCPServerInstance>();

  registerServer(config: MCPServerConfig): string {
    const serverId = `${config.name}-${Date.now()}`;
    const serverInstance: MCPServerInstance = {
      id: serverId,
      config,
      status: 'stopped',
      tools: [],
    };

    this.servers.set(serverId, serverInstance);
    this.logger.log(`Registered MCP server: ${config.name} with ID: ${serverId}`);
    return serverId;
  }

  getServer(serverId: string): MCPServerInstance | undefined {
    return this.servers.get(serverId);
  }

  getAllServers(): MCPServerInstance[] {
    return Array.from(this.servers.values());
  }

  updateServerStatus(serverId: string, status: MCPServerInstance['status']): void {
    const server = this.servers.get(serverId);
    if (server) {
      server.status = status;
    }
  }

  removeServer(serverId: string): boolean {
    return this.servers.delete(serverId);
  }
}

src/mcp/server/mcp-server.service.ts
import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { MCPServerRegistry } from './mcp-server.registry';
import { MCPServerConfig } from '../types/mcp.types';

@Injectable()
export class MCPServerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(MCPServerService.name);

  constructor(private readonly serverRegistry: MCPServerRegistry) {}

  async onModuleInit() {
    this.logger.log('Initializing MCP Server Service');
    await this.startConfiguredServers();
  }

  async onModuleDestroy() {
    this.logger.log('Destroying MCP Server Service');
    await this.stopAllServers();
  }

  async startServer(serverId: string): Promise<boolean> {
    const server = this.serverRegistry.getServer(serverId);
    if (!server) {
      this.logger.error(`Server not found: ${serverId}`);
      return false;
    }

    try {
      this.serverRegistry.updateServerStatus(serverId, 'starting');

      // Create StdioClientTransport
      const transport = new StdioClientTransport({
        command: server.config.command,
        args: server.config.args || [],
        env: server.config.env,
        cwd: server.config.cwd,
      });

      // Create MCP Client
      const client = new Client(
        {
          name: `nestjs-mcp-client-${server.config.name}`,
          version: '1.0.0',
        },
        {
          capabilities: {
            tools: {},
          },
        }
      );

      // Handle transport errors
      transport.onclose = () => {
        this.logger.warn(`Transport closed for server: ${serverId}`);
        this.serverRegistry.updateServerStatus(serverId, 'stopped');
      };

      transport.onerror = (error) => {
        this.logger.error(`Transport error for server ${serverId}:`, error);
        this.serverRegistry.updateServerStatus(serverId, 'error');
      };

      // Connect client to transport
      await client.connect(transport);

      // Store references
      server.transport = transport;
      server.client = client;

      this.serverRegistry.updateServerStatus(serverId, 'running');
      this.logger.log(`Started MCP server: ${serverId}`);

      return true;
    } catch (error) {
      this.logger.error(`Failed to start server ${serverId}:`, error);
      this.serverRegistry.updateServerStatus(serverId, 'error');
      return false;
    }
  }

  async stopServer(serverId: string): Promise<boolean> {
    const server = this.serverRegistry.getServer(serverId);
    if (!server || !server.client || !server.transport) {
      return false;
    }

    try {
      // Close the client connection
      await server.client.close();

      // Close the transport
      await server.transport.close();

      server.client = undefined;
      server.transport = undefined;

      this.serverRegistry.updateServerStatus(serverId, 'stopped');
      this.logger.log(`Stopped MCP server: ${serverId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error stopping server ${serverId}:`, error);
      return false;
    }
  }

  getServerClient(serverId: string): Client | undefined {
    const server = this.serverRegistry.getServer(serverId);
    return server?.client;
  }

  private async startConfiguredServers(): Promise<void> {
    // Load server configurations from config service or environment
    const serverConfigs: MCPServerConfig[] = [
      {
        name: 'filesystem',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-filesystem', '/tmp'],
      },
      {
        name: 'database',
        command: 'node',
        args: ['./mcp-servers/database-server.js'],
      },
      // Add more server configurations as needed
    ];

    const startPromises = serverConfigs.map(async (config) => {
      const serverId = this.serverRegistry.registerServer(config);
      return this.startServer(serverId);
    });

    const results = await Promise.allSettled(startPromises);
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        this.logger.error(`Failed to start server ${serverConfigs[index].name}:`, result.reason);
      }
    });
  }

  private async stopAllServers(): Promise<void> {
    const servers = this.serverRegistry.getAllServers();
    const stopPromises = servers
      .filter(server => server.status === 'running')
      .map(server => this.stopServer(server.id));

    await Promise.allSettled(stopPromises);
  }
}

MCP Client Module
src/mcp/client/mcp-client.module.ts
import { Module } from '@nestjs/common';
import { MCPClientService } from './mcp-client.service';
import { MCPClientRegistry } from './mcp-client.registry';
import { MCPServerModule } from '../server/mcp-server.module';

@Module({
  imports: [MCPServerModule],
  providers: [MCPClientService, MCPClientRegistry],
  exports: [MCPClientService, MCPClientRegistry],
})
export class MCPClientModule {}

src/mcp/client/mcp-client.registry.ts
import { Injectable, Logger } from '@nestjs/common';
import { MCPTool } from '../types/mcp.types';

@Injectable()
export class MCPClientRegistry {
  private readonly logger = new Logger(MCPClientRegistry.name);
  private tools = new Map<string, MCPTool>();

  registerTool(tool: MCPTool): void {
    this.tools.set(tool.name, tool);
    this.logger.log(`Registered MCP tool: ${tool.name} from server: ${tool.serverId}`);
  }

  getTool(name: string): MCPTool | undefined {
    return this.tools.get(name);
  }

  getAllTools(): MCPTool[] {
    return Array.from(this.tools.values());
  }

  getToolsByServer(serverId: string): MCPTool[] {
    return Array.from(this.tools.values()).filter(tool => tool.serverId === serverId);
  }

  removeTool(name: string): boolean {
    return this.tools.delete(name);
  }

  removeToolsByServer(serverId: string): void {
    const toolsToRemove = this.getToolsByServer(serverId);
    toolsToRemove.forEach(tool => this.removeTool(tool.name));
  }
}

src/mcp/client/mcp-client.service.ts
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ListToolsResult, CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import { MCPClientRegistry } from './mcp-client.registry';
import { MCPServerRegistry } from '../server/mcp-server.registry';
import { MCPServerService } from '../server/mcp-server.service';
import { MCPTool } from '../types/mcp.types';

@Injectable()
export class MCPClientService implements OnModuleInit {
  private readonly logger = new Logger(MCPClientService.name);

  constructor(
    private readonly clientRegistry: MCPClientRegistry,
    private readonly serverRegistry: MCPServerRegistry,
    private readonly serverService: MCPServerService,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing MCP Client Service');
    // Wait a bit for servers to start up
    setTimeout(() => this.discoverAllTools(), 2000);
  }

  async discoverToolsFromServer(serverId: string): Promise<void> {
    const client = this.serverService.getServerClient(serverId);
    if (!client) {
      this.logger.error(`No client available for server: ${serverId}`);
      return;
    }

    try {
      // List available tools from the server
      const toolsResult: ListToolsResult = await client.listTools();

      // Clear existing tools for this server
      this.clientRegistry.removeToolsByServer(serverId);

      // Register new tools
      for (const toolInfo of toolsResult.tools) {
        const tool: MCPTool = {
          name: toolInfo.name,
          description: toolInfo.description,
          inputSchema: toolInfo.inputSchema,
          serverId,
          handler: async (params: any) => {
            return await this.callTool(serverId, toolInfo.name, params);
          },
        };

        this.clientRegistry.registerTool(tool);
      }

      this.logger.log(`Discovered ${toolsResult.tools.length} tools from server: ${serverId}`);
    } catch (error) {
      this.logger.error(`Failed to discover tools from server ${serverId}:`, error);
    }
  }

  async callTool(serverId: string, toolName: string, params: any): Promise<any> {
    const client = this.serverService.getServerClient(serverId);
    if (!client) {
      throw new Error(`No client available for server: ${serverId}`);
    }

    try {
      const result: CallToolResult = await client.callTool({
        name: toolName,
        arguments: params,
      });

      return result.content;
    } catch (error) {
      this.logger.error(`Error calling tool ${toolName} on server ${serverId}:`, error);
      throw error;
    }
  }

  async executeToolFunction(toolName: string, params: any): Promise<any> {
    const tool = this.clientRegistry.getTool(toolName);
    if (!tool) {
      throw new Error(`Tool not found: ${toolName}`);
    }

    try {
      return await tool.handler(params);
    } catch (error) {
      this.logger.error(`Error executing tool ${toolName}:`, error);
      throw error;
    }
  }

  getAvailableTools(): MCPTool[] {
    return this.clientRegistry.getAllTools();
  }

  async refreshTools(): Promise<void> {
    const servers = this.serverRegistry.getAllServers();
    const runningServers = servers.filter(server => server.status === 'running');

    for (const server of runningServers) {
      await this.discoverToolsFromServer(server.id);
    }
  }

  private async discoverAllTools(): Promise<void> {
    const servers = this.serverRegistry.getAllServers();
    const runningServers = servers.filter(server => server.status === 'running');

    this.logger.log(`Discovering tools from ${runningServers.length} running servers`);

    for (const server of runningServers) {
      await this.discoverToolsFromServer(server.id);
    }
  }
}

OpenAI Integration Module
src/openai/openai.module.ts
import { Module } from '@nestjs/common';
import { OpenAIService } from './openai.service';
import { MCPClientModule } from '../mcp/client/mcp-client.module';

@Module({
  imports: [MCPClientModule],
  providers: [OpenAIService],
  exports: [OpenAIService],
})
export class OpenAIModule {}

src/openai/openai.service.ts
import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { MCPClientService } from '../mcp/client/mcp-client.service';

@Injectable()
export class OpenAIService {
  private readonly logger = new Logger(OpenAIService.name);
  private openai: OpenAI;

  constructor(private readonly mcpClientService: MCPClientService) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async createCompletion(messages: any[], model = 'gpt-4'): Promise<any> {
    const availableTools = this.mcpClientService.getAvailableTools();

    const tools = availableTools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.inputSchema,
      },
    }));

    try {
      const response = await this.openai.chat.completions.create({
        model,
        messages,
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
      });

      // Handle tool calls if present
      if (response.choices[0].message.tool_calls) {
        const toolResults = await this.handleToolCalls(
          response.choices[0].message.tool_calls
        );

        // Add tool results to conversation and get final response
        const updatedMessages = [
          ...messages,
          response.choices[0].message,
          ...toolResults,
        ];

        return await this.openai.chat.completions.create({
          model,
          messages: updatedMessages,
        });
      }

      return response;
    } catch (error) {
      this.logger.error('Error creating completion:', error);
      throw error;
    }
  }

  private async handleToolCalls(toolCalls: any[]): Promise<any[]> {
    const results = [];

    for (const toolCall of toolCalls) {
      try {
        const result = await this.mcpClientService.executeToolFunction(
          toolCall.function.name,
          JSON.parse(toolCall.function.arguments)
        );

        results.push({
          role: 'tool',
          tool_call_id: toolCall.id,
          content: JSON.stringify(result),
        });
      } catch (error) {
        this.logger.error(`Error executing tool ${toolCall.function.name}:`, error);
        results.push({
          role: 'tool',
          tool_call_id: toolCall.id,
          content: JSON.stringify({ error: error.message }),
        });
      }
    }

    return results;
  }
}

MCP Controller
src/mcp/mcp.controller.ts
import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { MCPClientService } from './client/mcp-client.service';
import { MCPServerService } from './server/mcp-server.service';

@Controller('mcp')
export class MCPController {
  constructor(
    private readonly clientService: MCPClientService,
    private readonly serverService: MCPServerService,
  ) {}

  @Get('tools')
  getAvailableTools() {
    return this.clientService.getAvailableTools();
  }

  @Post('tools/:toolName/execute')
  async executeTool(
    @Param('toolName') toolName: string,
    @Body() params: any,
  ) {
    return await this.clientService.executeToolFunction(toolName, params);
  }

  @Post('refresh-tools')
  async refreshTools() {
    await this.clientService.refreshTools();
    return { message: 'Tools refreshed successfully' };
  }

  @Get('servers')
  getServers() {
    return this.serverService.serverRegistry.getAllServers();
  }
}

Main App Module
src/app.module.ts
import { Module } from '@nestjs/common';
import { MCPServerModule } from './mcp/server/mcp-server.module';
import { MCPClientModule } from './mcp/client/mcp-client.module';
import { OpenAIModule } from './openai/openai.module';
import { MCPController } from './mcp/mcp.controller';

@Module({
  imports: [
    MCPServerModule,
    MCPClientModule,
    OpenAIModule,
  ],
  controllers: [MCPController],
})
export class AppModule {}

Environment Variables

Create a .env file:

OPENAI_API_KEY=your_openai_api_key_here

Key Features

Separation of Concerns: Clear separation between server management and client operations

Lifecycle Management: Proper startup/shutdown handling with OnModuleInit/OnModuleDestroy

Registry Pattern: Centralized tool and server registration

Error Handling: Comprehensive error handling and logging

Scalability: Easy to add new MCP servers and tools

Type Safety: Full TypeScript support with proper interfaces

MCP SDK Integration: Uses official StdioClientTransport for robust communication

REST API: Exposes endpoints for tool management and execution

Usage Examples
Starting the Application
npm run start:dev

API Endpoints

GET /mcp/tools - List all available tools

POST /mcp/tools/:toolName/execute - Execute a specific tool

POST /mcp/refresh-tools - Refresh tools from all servers

GET /mcp/servers - List all MCP servers and their status

Adding New MCP Servers

Modify the startConfiguredServers() method in MCPServerService to add new server configurations:

const serverConfigs: MCPServerConfig[] = [
  {
    name: 'filesystem',
    command: 'npx',
    args: ['-y', '@modelcontextprotocol/server-filesystem', '/tmp'],
  },
  {
    name: 'your-custom-server',
    command: 'node',
    args: ['./path/to/your/server.js'],
    env: { CUSTOM_ENV: 'value' },
  },
];


This architecture provides a solid foundation for MCP integration in NestJS while maintaining best practices and modularity.