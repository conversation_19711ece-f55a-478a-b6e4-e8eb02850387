# SW Search Agent

A sophisticated search agent that enables natural language querying of PostgreSQL databases using AI. Built with TypeScript, NestJS, and integrating OpenAI/LM Studio with Model Context Protocol (MCP) for secure database access.

## 🏗️ Architecture Overview

The application implements a **rolling messages array pattern** for AI workflows, where:
- System prompts initialize the conversation context
- User queries are added to the message history
- AI assistant responses and tool calls are tracked
- Database tool results are injected back as tool messages
- The conversation maintains context across multiple interactions

### Core Workflow
```
User Query → AI Assistant → Tool Calls → MCP Database → Tool Results → Final Response
     ↓                                                                        ↑
Message Array: [system, user, assistant, tool, assistant, tool, ...]
```

## 🛠️ Technology Stack

### Backend
- **Framework**: NestJS (Node.js)
- **Language**: TypeScript
- **AI Integration**: OpenAI SDK (compatible with LM Studio)
- **Database**: PostgreSQL (read-only access via MCP)
- **Protocol**: Model Context Protocol (MCP) with @modelcontextprotocol/server-postgres
- **MCP Architecture**: Independent server/client modules with local MCP server processes
- **Context System**: File-based system prompts and database metadata
- **Configuration**: Environment-based with @nestjs/config

### Frontend
- **UI**: Vanilla HTML/CSS/JavaScript
- **Styling**: Modern CSS with responsive design
- **Communication**: REST API with fetch()

### Development Tools
- **Build**: TypeScript compiler with NestJS CLI
- **Code Quality**: ESLint + Prettier
- **Process Management**: npm scripts with hot reload

## 📁 Project Structure

```
sw-search-agent/
├── src/
│   ├── main.ts                 # Application entry point
│   ├── app.module.ts           # Root module with all imports
│   ├── config/
│   │   └── configuration.ts    # Environment configuration loader
│   ├── auth/                   # Authentication module (placeholder)
│   │   └── auth.module.ts      # Future auth implementation
│   ├── openai/                 # OpenAI/LM Studio integration
│   │   ├── openai.module.ts    # OpenAI module definition
│   │   └── openai.service.ts   # Simplified OpenAI client service
│   ├── mcp/                    # Model Context Protocol integration
│   │   ├── mcp.module.ts       # Main MCP module
│   │   ├── server/             # MCP server management
│   │   │   ├── mcp-server.module.ts     # Server module
│   │   │   ├── mcp-server.service.ts    # Server lifecycle management
│   │   │   └── mcp-server.registry.ts   # Server registry
│   │   ├── client/             # MCP client operations
│   │   │   ├── mcp-client.module.ts     # Client module
│   │   │   ├── mcp-client.service.ts    # Client operations
│   │   │   └── mcp-client.registry.ts   # Tool registry
│   │   └── types/              # Shared types and interfaces
│   │       └── mcp.types.ts    # MCP type definitions
│   ├── context/                # File-based context system
│   │   ├── context.module.ts   # Context module definition
│   │   └── context.service.ts  # System prompt and metadata service
│   └── search/                 # Main search functionality
│       ├── search.module.ts    # Search module definition
│       ├── search.controller.ts # REST API endpoints
│       └── search.service.ts   # Core search logic
├── public/                     # Static web assets
│   ├── index.html             # Main UI page
│   ├── style.css              # Application styling
│   └── script.js              # Frontend JavaScript
├── docs/                      # Documentation and configuration
│   ├── database-description.md # Database metadata for AI context
│   ├── system-prompt.txt      # AI system prompt template
│   └── mcp-example.txt        # MCP implementation reference
├── scripts/                   # Development and deployment scripts
│   └── dev-setup.sh           # Development environment setup
├── .env                       # Environment variables
├── .env.example              # Environment template
├── package.json              # Dependencies and scripts
├── tsconfig.json             # TypeScript configuration
├── .eslintrc.js              # ESLint configuration
├── .prettierrc               # Prettier configuration
└── nest-cli.json             # NestJS CLI configuration
```

## 🔧 Configuration

### Environment Variables (.env)
```bash
# Application
PORT=3000
NODE_ENV=development

# OpenAI/LM Studio Configuration
OPENAI_API_KEY=lm-studio
OPENAI_BASE_URL=http://127.0.0.1:1234/v1
OPENAI_MODEL=devstral-small-2505-mlx
OPENAI_TEMPERATURE=0.1

# Context Configuration
CONTEXT_DESCRIPTION_FILE=docs/database-description.md
CONTEXT_SYSTEM_PROMPT_FILE=docs/system-prompt.txt

# PostgreSQL Database (for MCP server process only)
DATABASE_URL=postgresql://postgres:<EMAIL>:5433/sw_dev
```

## 🗄️ Database Integration

### Independent MCP Architecture
The application uses an independent Model Context Protocol implementation:

- **MCP Server Module**: Manages MCP server processes using `@modelcontextprotocol/server-postgres`
- **MCP Client Module**: Connects to MCP servers using official `@modelcontextprotocol/sdk`
- **Process Isolation**: MCP servers run as separate processes managed by the application
- **Zero Database Dependencies**: NestJS app has no direct database connectivity
- **Simplified Deployment**: Single application manages both MCP servers and clients

### File-Based Context System
- **External System Prompts**: AI prompts loaded from `docs/system-prompt.txt`
- **Database Metadata**: Additional context loaded from `docs/database-description.md`
- **Runtime Schema Discovery**: Actual database schema discovered by MCP server at runtime
- **Version Controlled**: All context files are part of the codebase and version controlled
- **Configurable Paths**: Customize file locations via environment variables
- **Always Enabled**: Static description inclusion is always active (no toggle needed)

### MCP Protocol Communication
The application implements the official Model Context Protocol:

#### Available Tools
1. **`read_query`**: Execute read-only SQL queries with enhanced safety and performance metrics
2. **`list_tables`**: List database tables and views with filtering and metadata
3. **`describe_table`**: Get detailed table schema, constraints, and indexes
4. **`list_schemas`**: List available database schemas
5. **`get_table_stats`**: Retrieve table statistics and column information

#### MCP Integration
The NestJS application manages MCP servers and clients using:
- `StdioClientTransport` for process-based communication
- Official `@modelcontextprotocol/sdk` client implementation
- Automatic tool discovery and OpenAI-compatible tool calling
- Independent server and client modules for separation of concerns

### Security Features
- **Read-only access**: Only SELECT statements are permitted by MCP server
- **Process isolation**: MCP servers run as separate processes
- **Zero database exposure**: NestJS app has no direct database credentials
- **Error handling**: Safe error propagation without sensitive data exposure

## 🌐 API Endpoints

### REST API
- `POST /api/search` - Main search endpoint
  - Body: `{ query: string, conversationId?: string }`
  - Returns: `{ response: string, conversationId: string, toolCalls: array }`

- `GET /api/search/conversation/:id` - Retrieve conversation history
- `DELETE /api/search/conversation/:id` - Clear conversation

### Static Assets
- `GET /` - Web UI (served from public/)
- `GET /style.css` - Application styles
- `GET /script.js` - Frontend JavaScript

## 🚀 Quick Start

### Development Setup
```bash
# Clone and setup
git clone <repository>
cd sw-search-agent
./scripts/dev-setup.sh

# Install dependencies
npm install

# Start development server
npm run dev

# Access the application
open http://localhost:3000
```

### Configuration
- **Environment Variables**: Configure via .env file
- **Database Connection**: Set DATABASE_URL for MCP server process
- **OpenAI/LM Studio**: Configure API endpoint and model

## 🚀 Available Scripts

```bash
# Development
npm run dev      # Start development server with hot reload
npm run start    # Start production server
npm run build    # Build the application
npm run lint     # Run ESLint
npm run format   # Format code with Prettier

# Setup
./scripts/dev-setup.sh       # Initialize development environment
```

## 📊 Logging

The application implements comprehensive logging across all modules:

- **Request/Response tracking** with timing metrics
- **Database operation logging** with query performance
- **AI interaction logging** with token usage
- **Tool execution tracking** with success/failure status
- **Error logging** with full stack traces

Log levels: `LOG`, `ERROR`, `WARN` with contextual information.

## 🔄 Message Flow Architecture

### Conversation Management
- **Conversation ID**: Unique identifier for each user session
- **Message Persistence**: In-memory storage of conversation history
- **Context Preservation**: System prompts and conversation state maintained

### Tool Integration Pattern
```typescript
// Rolling message array pattern
messages = [
  { role: 'system', content: 'System prompt...' },
  { role: 'user', content: 'User query' },
  { role: 'assistant', content: 'Response', tool_calls: [...] },
  { role: 'tool', content: 'Tool result', tool_call_id: 'id' },
  { role: 'assistant', content: 'Final response' }
]
```

## 🛡️ Security Considerations

- **Database Access**: Read-only PostgreSQL access with query validation
- **Environment Variables**: Sensitive configuration externalized
- **CORS**: Enabled for frontend communication
- **Error Handling**: Safe error messages without sensitive data exposure
- **Input Validation**: Query sanitization and type checking

## 🎯 Use Cases

- **Database Exploration**: "Show me all tables in the database"
- **Schema Analysis**: "What's the structure of the users table?"
- **Data Querying**: "Find all products with price greater than 100"
- **Analytics**: "How many records are in each table?"
- **Relationship Discovery**: "Show me the foreign key relationships"

## 🔮 Future Enhancements

- **Authentication**: User management and access control
- **Query Caching**: Redis integration for performance
- **Advanced MCP Tools**: Write operations, stored procedures
- **Multi-database Support**: Multiple PostgreSQL instances
- **Export Functionality**: CSV, JSON data export
- **Query History**: Persistent conversation storage
- **Remote MCP Servers**: Support for remote MCP server connections
- **Advanced Context**: Dynamic schema updates and relationship discovery

## 📝 Database Context Configuration

The AI assistant uses a static database description file to understand your database structure without requiring direct database access for schema discovery.

### Configuring Database Description

1. **Edit the description file**: `docs/database-description.md`
2. **Describe your tables**: Include table names, key columns, and relationships
3. **Explain query patterns**: Help the AI understand how to join tables effectively
4. **Use clear language**: Write descriptions that help the AI generate accurate queries

### Example Description Format

```markdown
# Database Structure Overview

## Core Tables

### Athletes and Teams
- **master_athlete table**: Contains athlete information including names and identifiers
  - Use `master_athlete_id` to retrieve athlete names and details

### Team Management
- **roster_team table**: Contains event team information and roster data
  - Event teams are located in this table
  - Links athletes to specific teams and events

## Key Relationships
- Athletes are referenced throughout the system using `master_athlete_id`
- Use JOIN operations to connect athlete names with their team assignments
```

### Custom Description File

To use a different description file, set the environment variable:
```bash
CONTEXT_DESCRIPTION_FILE=path/to/your/custom-description.md
```

## 🔄 Recent Major Changes

### Independent MCP Architecture (v6.0)
- **Complete MCP Redesign**: Implemented independent server and client modules
- **Process-Based Communication**: Uses `StdioClientTransport` for local MCP server processes
- **Separation of Concerns**: Clear separation between server management and client operations
- **OpenAI-Compatible Types**: Direct use of OpenAI types to avoid unnecessary mappings
- **Zero Database Dependencies**: NestJS app completely isolated from database connectivity
- **Simplified Deployment**: Single application manages both MCP servers and clients
- **Official MCP SDK**: Uses `@modelcontextprotocol/sdk` and `@modelcontextprotocol/server-postgres`
- **Registry Pattern**: Centralized tool and server registration with proper lifecycle management

### Key Questions for Future Development

1. **Database Schema Evolution**: How should the system handle database schema changes during runtime? Should it auto-refresh or require manual triggers?

2. **Context Optimization**: What's the optimal balance between including comprehensive schema information vs. performance? Should we implement smart context filtering based on query patterns?

3. **Multi-Database Strategy**: When adding support for multiple databases, should each have its own MCP server instance, or should we implement a unified multi-database MCP server?

4. **Security Model**: What authentication and authorization model should be implemented? Role-based access to specific tables/schemas?

5. **Performance Scaling**: At what point should we implement query result caching, and what caching strategy would work best with the conversational AI pattern?

6. **Remote MCP Integration**: What protocols and security measures should be implemented for remote MCP server connections? How should we handle network failures and reconnection?

7. **Context Personalization**: Should the system learn from user query patterns to automatically adjust context inclusion for better AI responses?
