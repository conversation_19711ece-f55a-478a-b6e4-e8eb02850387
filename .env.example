# Application
PORT=3000
NODE_ENV=development

# OpenAI/LM Studio Configuration
OPENAI_API_KEY=lm-studio
OPENAI_BASE_URL=http://127.0.0.1:1234/v1
OPENAI_MODEL=mistralai/devstral-small-2505
OPENAI_TEMPERATURE=0.1

# PostgreSQL Database (for MCP server only)
DATABASE_URL=postgresql://postgres:<EMAIL>:5433/sw_dev

# Context Configuration
CONTEXT_DESCRIPTION_FILE=docs/database-description.md
CONTEXT_SYSTEM_PROMPT_FILE=docs/system-prompt.txt
